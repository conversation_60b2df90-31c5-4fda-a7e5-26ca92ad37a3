"""
Teste do sistema avançado de minimapa
Detecta aliados (azuis), inimigos (vermelhos), torres e estratégia de waves
"""
import time
from game.lol_detector import lol_detector
from game.advanced_minimap import advanced_minimap, GameState, UnitType
from game.combat_system import combat_system
from utils.logger import logger

def test_unit_detection():
    """Testar detecção de unidades (azuis, vermelhos, torres)"""
    print("🔍 Teste de Detecção de Unidades")
    print("=" * 35)
    
    if advanced_minimap.reference_map is not None:
        print("✅ Mapa de referência carregado!")
        
        # Analisar mapa de referência
        units = advanced_minimap.detect_units(advanced_minimap.reference_map)
        
        # Separar por tipo
        ally_minions = [u for u in units if u.unit_type == UnitType.ALLY_MINION]
        ally_champions = [u for u in units if u.unit_type == UnitType.ALLY_CHAMPION]
        enemy_minions = [u for u in units if u.unit_type == UnitType.ENEMY_MINION]
        enemy_champions = [u for u in units if u.unit_type == UnitType.ENEMY_CHAMPION]
        towers = [u for u in units if u.unit_type == UnitType.TOWER]
        
        print(f"\n📊 Unidades detectadas no mapa:")
        print(f"   🔵 Minions aliados: {len(ally_minions)}")
        print(f"   👤 Campeões aliados: {len(ally_champions)}")
        print(f"   🔴 Minions inimigos: {len(enemy_minions)}")
        print(f"   ⚔️ Campeões inimigos: {len(enemy_champions)}")
        print(f"   🏗️ Torres: {len(towers)}")
        
        if units:
            print(f"\n📋 Detalhes das unidades:")
            for i, unit in enumerate(units[:15], 1):  # Mostrar apenas os 15 primeiros
                moving_text = "🏃 MOVENDO" if unit.is_moving else "⏸️ PARADO"
                print(f"   {i:2d}. {unit.unit_type.value:15s} em {unit.position} - {moving_text} - Tamanho: {unit.size}")
        
        # Salvar debug
        advanced_minimap.save_debug_minimap(advanced_minimap.reference_map, units)
        print(f"\n💾 Debug salvo em: screenshots/advanced_minimap_debug.png")
        
        return True
    else:
        print("❌ Mapa de referência não encontrado!")
        print("⚠️ Certifique-se que o arquivo 'screenshots/mapa.png' existe")
        return False

def test_game_state_analysis():
    """Testar análise de estado do jogo"""
    print("\n🧠 Teste de Análise de Estado do Jogo")
    print("=" * 40)
    
    if not lol_detector.ensure_lol_ready():
        print("❌ LoL não está pronto!")
        return False
    
    print("🎮 Analisando estado do jogo em tempo real...")
    print("⚠️ Entre em uma partida para ver resultados")
    print("⚠️ Pressione Ctrl+C para parar")
    
    frame_count = 0
    state_changes = 0
    last_state = None
    
    try:
        while True:
            frame_count += 1
            
            # Capturar minimapa
            minimap = advanced_minimap.capture_minimap()
            
            if minimap is not None:
                # Detectar unidades
                units = advanced_minimap.detect_units(minimap)
                
                # Analisar estado
                current_state = advanced_minimap.analyze_game_state(units)
                
                if current_state != last_state:
                    state_changes += 1
                    print(f"\n🔄 MUDANÇA DE ESTADO #{state_changes}: {current_state.value}")
                    last_state = current_state
                
                # Separar unidades
                ally_minions = [u for u in units if u.unit_type == UnitType.ALLY_MINION]
                enemy_champions = [u for u in units if u.unit_type == UnitType.ENEMY_CHAMPION]
                towers = [u for u in units if u.unit_type == UnitType.TOWER]
                
                # Status resumido
                print(f"\rFrame {frame_count:3d} | "
                      f"Estado: {current_state.value:20s} | "
                      f"🔵 {len(ally_minions):2d} | "
                      f"⚔️ {len(enemy_champions):2d} | "
                      f"🏗️ {len(towers):2d}", 
                      end="", flush=True)
                
                # Salvar debug a cada 30 frames
                if frame_count % 30 == 0 and units:
                    advanced_minimap.save_debug_minimap(minimap, units)
                
            else:
                print(f"\rFrame {frame_count:3d} | ❌ Falha na captura", end="", flush=True)
            
            time.sleep(0.5)
    
    except KeyboardInterrupt:
        print(f"\n\n📊 Teste finalizado:")
        print(f"   Frames processados: {frame_count}")
        print(f"   Mudanças de estado: {state_changes}")
        print(f"   Estado final: {last_state.value if last_state else 'Nenhum'}")

def test_strategic_actions():
    """Testar ações estratégicas"""
    print("\n⚔️ Teste de Ações Estratégicas")
    print("=" * 32)
    
    if not lol_detector.ensure_lol_ready():
        print("❌ LoL não está pronto!")
        return False
    
    print("🤖 Testando ações estratégicas por 30 segundos...")
    print("⚠️ O bot irá executar ações baseadas no minimapa!")
    print("⚠️ Pressione Ctrl+C para parar")
    
    actions_taken = 0
    attacks = 0
    retreats = 0
    movements = 0
    
    try:
        start_time = time.time()
        while time.time() - start_time < 30:
            # Capturar minimapa
            minimap = advanced_minimap.capture_minimap()
            
            if minimap is not None:
                # Detectar unidades
                units = advanced_minimap.detect_units(minimap)
                
                # Obter ação estratégica
                strategic_action = advanced_minimap.get_strategic_action(units)
                
                if strategic_action and strategic_action.get('target_position'):
                    actions_taken += 1
                    action_type = strategic_action.get('action_type', 'move')
                    description = strategic_action.get('description', 'Ação')
                    
                    if action_type == 'attack':
                        attacks += 1
                        print(f"\n⚔️ ATAQUE #{attacks}: {description}")
                    elif action_type == 'retreat':
                        retreats += 1
                        print(f"\n🏃 RECUO #{retreats}: {description}")
                    elif action_type == 'move':
                        movements += 1
                        print(f"\n🚶 MOVIMENTO #{movements}: {description}")
                    
                    # Mostrar estado atual
                    status = advanced_minimap.get_current_status()
                    print(f"   Estado: {status['current_state']}")
                    print(f"   Minions aliados: {status['ally_minions_alive']}")
                    print(f"   Inimigos: {status['enemy_units']}")
                
                # Status resumido
                print(f"\rAções: {actions_taken:2d} | "
                      f"⚔️ {attacks:2d} | 🏃 {retreats:2d} | 🚶 {movements:2d}", 
                      end="", flush=True)
            
            time.sleep(0.5)
    
    except KeyboardInterrupt:
        pass
    
    print(f"\n\n📊 Resultados das ações estratégicas:")
    print(f"   Total de ações: {actions_taken}")
    print(f"   Ataques: {attacks}")
    print(f"   Recuos: {retreats}")
    print(f"   Movimentos: {movements}")

def test_wave_management():
    """Testar gerenciamento de waves"""
    print("\n🌊 Teste de Gerenciamento de Waves")
    print("=" * 35)
    
    if not lol_detector.ensure_lol_ready():
        print("❌ LoL não está pronto!")
        return False
    
    print("📊 Monitorando waves de minions por 60 segundos...")
    print("⚠️ Observe o comportamento quando minions morrem")
    print("⚠️ Pressione Ctrl+C para parar")
    
    wave_events = []
    min_minions_seen = 999
    max_minions_seen = 0
    
    try:
        start_time = time.time()
        while time.time() - start_time < 60:
            # Capturar minimapa
            minimap = advanced_minimap.capture_minimap()
            
            if minimap is not None:
                # Detectar unidades
                units = advanced_minimap.detect_units(minimap)
                ally_minions = [u for u in units if u.unit_type == UnitType.ALLY_MINION]
                
                minion_count = len(ally_minions)
                min_minions_seen = min(min_minions_seen, minion_count)
                max_minions_seen = max(max_minions_seen, minion_count)
                
                # Detectar eventos de wave
                if minion_count <= 2:
                    wave_events.append(('LOW_MINIONS', time.time(), minion_count))
                    print(f"\n⚠️ POUCOS MINIONS: {minion_count} restantes!")
                elif minion_count >= 5:
                    wave_events.append(('NEW_WAVE', time.time(), minion_count))
                    print(f"\n🌊 NOVA WAVE: {minion_count} minions detectados!")
                
                # Analisar estado
                game_state = advanced_minimap.analyze_game_state(units)
                
                # Status resumido
                print(f"\rMinions: {minion_count:2d} | "
                      f"Estado: {game_state.value:20s} | "
                      f"Min: {min_minions_seen:2d} | Max: {max_minions_seen:2d}", 
                      end="", flush=True)
            
            time.sleep(1.0)
    
    except KeyboardInterrupt:
        pass
    
    print(f"\n\n📊 Análise de waves:")
    print(f"   Mínimo de minions visto: {min_minions_seen}")
    print(f"   Máximo de minions visto: {max_minions_seen}")
    print(f"   Eventos de wave: {len(wave_events)}")
    
    if wave_events:
        print(f"\n📋 Eventos detectados:")
        for event_type, timestamp, count in wave_events[-10:]:  # Últimos 10 eventos
            print(f"   {event_type}: {count} minions")

def test_combat_integration():
    """Testar integração com sistema de combate"""
    print("\n🤖 Teste de Integração com Combate Avançado")
    print("=" * 45)
    
    if not lol_detector.ensure_lol_ready():
        print("❌ LoL não está pronto!")
        return False
    
    print("⚔️ Testando combate integrado com minimapa avançado...")
    print("⚠️ O bot irá executar estratégias completas!")
    print("⚠️ Pressione Ctrl+C para parar")
    
    combat_actions = 0
    strategic_actions = 0
    
    try:
        start_time = time.time()
        while time.time() - start_time < 30:
            # Testar sistema de combate integrado
            combat_action = combat_system.update_combat()
            
            if combat_action:
                combat_actions += 1
                
                # Obter status detalhado
                status = combat_system.get_minimap_status()
                
                print(f"\n🤖 AÇÃO DE COMBATE #{combat_actions}")
                print(f"   Estado avançado: {status.get('advanced_state', 'unknown')}")
                print(f"   Minions aliados: {status.get('ally_minions', 0)}")
                print(f"   Inimigos: {status.get('enemy_units', 0)}")
                print(f"   Torres: {status.get('towers_detected', 0)}")
                
                strategic_actions += 1
            
            # Status resumido
            print(f"\rCombate: {combat_actions:2d} | Estratégicas: {strategic_actions:2d}", 
                  end="", flush=True)
            
            time.sleep(0.3)
    
    except KeyboardInterrupt:
        pass
    
    print(f"\n\n📊 Resultados da integração:")
    print(f"   Ações de combate: {combat_actions}")
    print(f"   Ações estratégicas: {strategic_actions}")

def main():
    """Menu principal"""
    print("🗺️ Teste do Sistema Avançado de Minimapa - League of Legends")
    print("Detecta aliados (azuis), inimigos (vermelhos), torres e gerencia waves")
    print("=" * 70)
    
    while True:
        print("\n📋 Opções de Teste:")
        print("1. Testar detecção de unidades (mapa.png)")
        print("2. Testar análise de estado do jogo")
        print("3. Testar ações estratégicas")
        print("4. Testar gerenciamento de waves")
        print("5. Testar integração com combate")
        print("6. Status do sistema")
        print("7. Sair")
        
        choice = input("\nEscolha uma opção (1-7): ").strip()
        
        try:
            if choice == "1":
                success = test_unit_detection()
                if success:
                    print("✅ Teste de detecção concluído!")
                else:
                    print("❌ Falha no teste de detecção")
            
            elif choice == "2":
                test_game_state_analysis()
            
            elif choice == "3":
                test_strategic_actions()
            
            elif choice == "4":
                test_wave_management()
            
            elif choice == "5":
                test_combat_integration()
            
            elif choice == "6":
                status = advanced_minimap.get_current_status()
                print("\n📊 Status do Sistema Avançado:")
                for key, value in status.items():
                    print(f"   {key}: {value}")
                
                # Status do LoL
                lol_status = lol_detector.get_lol_status()
                print(f"\n📊 Status do LoL:")
                for key, value in lol_status.items():
                    icon = "✅" if value else "❌"
                    print(f"   {icon} {key}: {value}")
            
            elif choice == "7":
                print("👋 Saindo...")
                break
            
            else:
                print("❌ Opção inválida!")
                
        except KeyboardInterrupt:
            print("\n⏸️ Teste interrompido")
        except Exception as e:
            print(f"❌ Erro: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Teste interrompido")
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
    
    input("\nPressione Enter para sair...")
