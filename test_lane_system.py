"""
Teste do sistema de lanes
"""
import time
from game.lane_system import lane_system, Lane
from game.auto_movement import auto_movement
from utils.logger import logger

def test_lane_system():
    """Testar sistema de lanes"""
    print("🛣️ Teste do Sistema de Lanes")
    print("=" * 30)
    
    # Testar configuração de lane
    print("\n1. Testando configuração de lane MID:")
    auto_movement.set_preferred_lane("mid")
    auto_movement.show_debug_status()
    
    print("\n2. Testando movimento de lane:")
    for i in range(5):
        print(f"\n--- Movimento {i+1} ---")
        pos = lane_system.update_lane_movement()
        print(f"Posição retornada: {pos}")
        
        status = lane_system.get_current_status()
        print(f"Status: Lane={status['current_lane']}, Posição={status['current_position']}")
        
        time.sleep(1)
    
    print("\n3. Testando mudança para TOP:")
    auto_movement.set_preferred_lane("top")
    auto_movement.show_debug_status()
    
    print("\n4. Testando movimento TOP:")
    for i in range(3):
        print(f"\n--- Movimento TOP {i+1} ---")
        pos = lane_system.update_lane_movement()
        print(f"Posição retornada: {pos}")
        
        status = lane_system.get_current_status()
        print(f"Status: Lane={status['current_lane']}, Posição={status['current_position']}")
        
        time.sleep(1)
    
    print("\n✅ Teste concluído!")

if __name__ == "__main__":
    test_lane_system()
