"""
Configurações do Bot League of Legends
"""
import os
from dataclasses import dataclass
from typing import Tu<PERSON>, Dict, Any

@dataclass
class GameConfig:
    """Configurações relacionadas ao jogo"""
    # Resolução da tela (ajustar conforme sua resolução)
    SCREEN_WIDTH: int = 1920
    SCREEN_HEIGHT: int = 1080
    
    # Configurações de delay (em segundos)
    MIN_DELAY: float = 0.1
    MAX_DELAY: float = 0.3
    ACTION_DELAY: float = 0.5
    
    # Configurações do cliente LoL
    CLIENT_STARTUP_DELAY: int = 30
    GAME_LOADING_DELAY: int = 60
    
    # Nível alvo
    TARGET_LEVEL: int = 30
    
    # Configurações de segurança
    HUMANIZE_MOVEMENTS: bool = True
    RANDOM_DELAYS: bool = True
    
@dataclass
class Coordinates:
    """Coordenadas importantes da interface - Coordenadas completas confirmadas"""
    # Menu principal
    PLAY_BUTTON: Tuple[int, int] = (442, 197)  # Botão JOGAR

    # Seleção de modo de jogo
    BOTS_MODE_BUTTON: Tuple[int, int] = (473, 253)  # Modo de jogo BOTS
    CONFIRM_MODE_BUTTON: Tuple[int, int] = (865, 842)  # Confirmar modo de jogo
    FIND_MATCH_BUTTON: Tuple[int, int] = (866, 843)  # Encontrar partida
    ACCEPT_BUTTON: Tuple[int, int] = (950, 708)  # Aceitar partida

    # Seleção de campeão
    CHAMPION_SEARCH_BOX: Tuple[int, int] = (1103, 259)  # Caixa de pesquisa
    CONFIRM_CHAMPION_BUTTON: Tuple[int, int] = (958, 764)  # Confirmar campeão
    
@dataclass
class Champions:
    """Lista de campeões recomendados para bot"""
    EASY_CHAMPIONS = [
        "Garen",
        "Annie",
        "Ashe",
        "Master Yi",
        "Warwick"
    ]

    DEFAULT_CHAMPION = "Annie"

    # Lista completa baseada na sua lista
    ALL_CHAMPIONS = [
        "Ahri", "Akali", "Alistar", "Ammu", "Anivia", "Annie",
        "Ashe", "Azir", "Bard", "Blitzcrank", "Brand", "Braum",
        "Caitlyn", "Cassiopeia", "Cho'Gath", "Darius", "Diana",
        "Dr. Mundo", "Draven", "Ekko", "Elise", "Evelynn", "Ezreal",
        "Fiddlesticks", "Fiora", "Fizz", "Galio", "Garen", "Gnar",
        "Gragas", "Graves", "Gwen", "Hecarim", "Illaoi", "Irelia",
        "Janna", "Jarvan IV", "Jax", "Jayce", "Jhin", "Jinx",
        "Kai'Sa", "Kalista", "Karma", "Karthus", "Kassadin",
        "Katarina", "Kayle", "Kennen", "Kha'Zix", "Kindred",
        "Kled", "Kog'Maw", "LeBlanc", "Lee Sin", "Leona",
        "Lissandra", "Lucian", "Lulu", "Lux", "Malphite",
        "Malzahar", "Maokai", "Master Yi", "Miss Fortune",
        "Mordekaiser", "Morgana", "Nami", "Nasus", "Nautilus",
        "Neeko", "Nidalee", "Nocturne", "Nunu", "Olaf",
        "Orianna", "Ornn", "Pantheon", "Poppy", "Pyke",
        "Qiyana", "Quinn", "Rakan", "Rammus", "Rek'Sai",
        "Rell", "Renekton", "Rengar", "Riven", "Rumble",
        "Ryze", "Samira", "Sejuani", "Senna", "Seraphine",
        "Sett", "Shaco", "Shen", "Shyvana", "Singed", "Sion",
        "Sivir", "Skarner", "Sona", "Soraka", "Swain", "Sylas",
        "Syndra", "Tahm Kench", "Taliyah", "Talon", "Taric",
        "Teemo", "Thresh", "Tristana", "Trundle", "Tryndamere",
        "Twisted Fate", "Twitch", "Udyr", "Urgot", "Varus",
        "Vayne", "Veigar", "Vel'Koz", "Vex", "Vi", "Viego",
        "Viktor", "Vladimir", "Volibear", "Warwick", "Wukong",
        "Xayah", "Xerath", "Xin Zhao", "Yasuo", "Yone",
        "Yorick", "Yuumi", "Zac", "Zed", "Zeri", "Ziggs",
        "Zilean", "Zoe", "Zyra"
    ]

@dataclass
class Items:
    """Itens recomendados por tipo de campeão"""
    GAREN_BUILD = [
        "Doran's Shield",
        "Berserker's Greaves", 
        "Stridebreaker",
        "Dead Man's Plate",
        "Force of Nature"
    ]
    
    ANNIE_BUILD = [
        "Doran's Ring",
        "Sorcerer's Shoes",
        "Luden's Tempest", 
        "Zhonya's Hourglass",
        "Rabadon's Deathcap"
    ]

# Configuração global
config = GameConfig()
coords = Coordinates()
champions = Champions()
items = Items()
