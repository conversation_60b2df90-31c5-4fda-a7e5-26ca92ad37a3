"""
Teste APENAS do movimento de lane, sem sistema de combate
"""
import time
from game.lane_system import lane_system, Lane
from utils.logger import logger

def test_pure_lane_movement():
    """Testar movimento puro de lane"""
    print("🛣️ Teste PURO de Movimento de Lane")
    print("=" * 40)
    
    # Configurar lane MID
    print("\n1. Configurando lane MID:")
    lane_system.set_preferred_lane(Lane.MID)
    
    # Mostrar posições da lane
    mid_positions = lane_system.lane_positions[Lane.MID]
    print(f"Posições MID disponíveis:")
    for pos_type, pos_obj in mid_positions.items():
        print(f"  {pos_type}: ({pos_obj.x}, {pos_obj.y})")
    
    print(f"\n2. Testando movimento MID (10 movimentos):")
    for i in range(10):
        print(f"\n--- Movimento {i+1} ---")
        
        # Obter posição do sistema de lane
        pos = lane_system.update_lane_movement()
        status = lane_system.get_current_status()
        
        print(f"Posição retornada: {pos}")
        print(f"Lane: {status['current_lane']}")
        print(f"Tipo: {status['current_position']}")
        print(f"Padrão índice: {status['pattern_index']}")
        
        time.sleep(1.5)  # Pausa entre movimentos
    
    print(f"\n3. Testando lane TOP:")
    lane_system.set_preferred_lane(Lane.TOP)
    
    for i in range(5):
        print(f"\n--- TOP Movimento {i+1} ---")
        
        pos = lane_system.update_lane_movement()
        status = lane_system.get_current_status()
        
        print(f"Posição TOP: {pos}")
        print(f"Tipo: {status['current_position']}")
        
        time.sleep(1.5)
    
    print(f"\n4. Testando lane BOT:")
    lane_system.set_preferred_lane(Lane.BOT)
    
    for i in range(5):
        print(f"\n--- BOT Movimento {i+1} ---")
        
        pos = lane_system.update_lane_movement()
        status = lane_system.get_current_status()
        
        print(f"Posição BOT: {pos}")
        print(f"Tipo: {status['current_position']}")
        
        time.sleep(1.5)
    
    print("\n✅ Teste de movimento puro concluído!")
    print("\n📋 VERIFICAÇÕES:")
    print("- ✅ Cada lane tem posições diferentes?")
    print("- ✅ Movimento segue padrão [farm, farm, aggressive, safe]?")
    print("- ✅ Coordenadas mudam entre movimentos?")
    print("- ✅ Sistema responde à mudança de lane?")

def test_simple_auto_movement():
    """Testar auto_movement sem combate"""
    print("\n" + "="*50)
    print("🤖 Teste de Auto Movement SEM Combate")
    print("="*50)
    
    from game.auto_movement import auto_movement
    
    # Configurar
    auto_movement.set_preferred_lane("mid")
    auto_movement.start_auto_movement("lane")
    
    print("\nStatus inicial:")
    auto_movement.show_debug_status()
    
    print(f"\n5. Simulando update_movement (sem combate):")
    
    # Desabilitar combate temporariamente
    original_update_combat = None
    try:
        from game import combat_system
        original_update_combat = combat_system.combat_system.update_combat
        combat_system.combat_system.update_combat = lambda: False  # Sempre retorna False
        
        for i in range(8):
            print(f"\n--- Auto Movement {i+1} ---")
            
            auto_movement.update_movement()
            
            status = auto_movement.get_status()
            lane_info = status['lane_info']
            
            print(f"Padrão: {status['pattern']}")
            print(f"Lane: {lane_info['current_lane']}")
            print(f"Posição: {lane_info['current_position']}")
            print(f"Coordenadas: {lane_info['current_coordinates']}")
            
            time.sleep(2.5)  # Aguardar intervalo de movimento
            
    finally:
        # Restaurar combate
        if original_update_combat:
            combat_system.combat_system.update_combat = original_update_combat
    
    auto_movement.stop_auto_movement()
    print("\n✅ Teste de auto movement concluído!")

if __name__ == "__main__":
    test_pure_lane_movement()
    test_simple_auto_movement()
