"""
Sistema de detecção visual para gameplay
Baseado na imagem scriptbot.png com círculos coloridos
Captura tela real do jogo
"""
import cv2
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
from utils.logger import logger
from game.lol_detector import lol_detector

@dataclass
class DetectedTarget:
    """Representa um alvo detectado"""
    type: str  # 'enemy_champion', 'enemy_minion', 'low_health'
    position: Tuple[int, int]  # (x, y)
    confidence: float  # 0.0 - 1.0
    size: int  # Tamanho do círculo detectado
    color: str  # 'green', 'white', 'red'

class VisualDetector:
    """Detector visual para gameplay baseado em círculos coloridos"""
    
    def __init__(self):
        # Configurações de cores (HSV)
        self.color_ranges = {
            'green': {  # Círculo verde = Inimigos (campeões)
                'lower': np.array([40, 50, 50]),
                'upper': np.array([80, 255, 255])
            },
            'white': {  # Círculo branco = Tropas/Minions inimigos
                'lower': np.array([0, 0, 200]),
                'upper': np.array([180, 30, 255])
            },
            'red': {    # Círculo vermelho = Vida baixa
                'lower': np.array([0, 50, 50]),
                'upper': np.array([10, 255, 255])
            }
        }
        
        # Configurações de detecção
        self.min_circle_radius = 5
        self.max_circle_radius = 50
        self.min_contour_area = 50
        
        logger.info("Sistema de detecção visual inicializado")
    
    def detect_targets(self, screenshot=None) -> List[DetectedTarget]:
        """
        Detectar todos os alvos na tela
        
        Returns:
            Lista de alvos detectados
        """
        try:
            # Capturar tela se não fornecida
            if screenshot is None:
                # Verificar se LoL está rodando
                if not lol_detector.is_lol_running():
                    logger.warning("League of Legends não está rodando")
                    return []

                # Capturar tela específica do LoL
                screenshot = lol_detector.capture_lol_screen()
                if screenshot is None:
                    logger.warning("Falha ao capturar tela do LoL")
                    return []
            
            # Converter para HSV
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            detected_targets = []
            
            # Detectar cada tipo de círculo
            for color_name, color_range in self.color_ranges.items():
                targets = self._detect_colored_circles(hsv, color_name, color_range)
                detected_targets.extend(targets)
            
            # Ordenar por prioridade (inimigos > vida baixa > minions)
            detected_targets.sort(key=self._get_target_priority, reverse=True)
            
            logger.debug(f"Detectados {len(detected_targets)} alvos")
            return detected_targets
            
        except Exception as e:
            logger.error(f"Erro na detecção de alvos: {e}")
            return []
    
    def _detect_colored_circles(self, hsv_image: np.ndarray, color_name: str, color_range: Dict) -> List[DetectedTarget]:
        """Detectar círculos de uma cor específica"""
        try:
            # Criar máscara para a cor
            mask = cv2.inRange(hsv_image, color_range['lower'], color_range['upper'])
            
            # Aplicar operações morfológicas para limpar a máscara
            kernel = np.ones((3, 3), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            
            # Encontrar contornos
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            targets = []
            
            for contour in contours:
                # Filtrar por área mínima
                area = cv2.contourArea(contour)
                if area < self.min_contour_area:
                    continue
                
                # Verificar se é aproximadamente circular
                perimeter = cv2.arcLength(contour, True)
                if perimeter == 0:
                    continue
                
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                
                # Aceitar formas razoavelmente circulares
                if circularity > 0.3:
                    # Calcular centro e raio
                    (x, y), radius = cv2.minEnclosingCircle(contour)
                    center = (int(x), int(y))
                    radius = int(radius)
                    
                    # Filtrar por tamanho
                    if self.min_circle_radius <= radius <= self.max_circle_radius:
                        # Determinar tipo de alvo baseado na cor
                        target_type = self._get_target_type(color_name)
                        
                        # Calcular confiança baseada na circularidade e tamanho
                        confidence = min(circularity, 1.0) * (area / 1000.0)
                        confidence = min(confidence, 1.0)
                        
                        target = DetectedTarget(
                            type=target_type,
                            position=center,
                            confidence=confidence,
                            size=radius,
                            color=color_name
                        )
                        
                        targets.append(target)
            
            return targets
            
        except Exception as e:
            logger.error(f"Erro ao detectar círculos {color_name}: {e}")
            return []
    
    def _get_target_type(self, color_name: str) -> str:
        """Converter cor para tipo de alvo"""
        color_to_type = {
            'green': 'enemy_champion',
            'white': 'enemy_minion',
            'red': 'low_health'
        }
        return color_to_type.get(color_name, 'unknown')
    
    def _get_target_priority(self, target: DetectedTarget) -> float:
        """Calcular prioridade do alvo"""
        priority_map = {
            'enemy_champion': 100.0,  # Prioridade máxima
            'low_health': 80.0,       # Alta prioridade
            'enemy_minion': 50.0,     # Prioridade média
            'unknown': 0.0
        }
        
        base_priority = priority_map.get(target.type, 0.0)
        return base_priority * target.confidence
    
    def get_nearest_enemy(self, player_position: Tuple[int, int] = None) -> Optional[DetectedTarget]:
        """
        Obter inimigo mais próximo
        
        Args:
            player_position: Posição do jogador (padrão: centro da tela)
        
        Returns:
            Inimigo mais próximo ou None
        """
        if player_position is None:
            # Assumir centro da tela como posição do jogador
            player_position = (960, 540)
        
        targets = self.detect_targets()
        enemies = [t for t in targets if t.type == 'enemy_champion']
        
        if not enemies:
            return None
        
        # Encontrar o mais próximo
        def distance(target):
            dx = target.position[0] - player_position[0]
            dy = target.position[1] - player_position[1]
            return (dx * dx + dy * dy) ** 0.5
        
        nearest = min(enemies, key=distance)
        return nearest
    
    def get_farmable_minions(self, max_distance: int = 300) -> List[DetectedTarget]:
        """
        Obter minions que podem ser farmados
        
        Args:
            max_distance: Distância máxima para considerar
        
        Returns:
            Lista de minions próximos
        """
        player_position = (960, 540)  # Centro da tela
        
        targets = self.detect_targets()
        minions = [t for t in targets if t.type == 'enemy_minion']
        
        # Filtrar por distância
        nearby_minions = []
        for minion in minions:
            dx = minion.position[0] - player_position[0]
            dy = minion.position[1] - player_position[1]
            distance = (dx * dx + dy * dy) ** 0.5
            
            if distance <= max_distance:
                nearby_minions.append(minion)
        
        # Ordenar por distância
        nearby_minions.sort(key=lambda m: (
            (m.position[0] - player_position[0]) ** 2 + 
            (m.position[1] - player_position[1]) ** 2
        ))
        
        return nearby_minions
    
    def is_low_health(self) -> bool:
        """
        Verificar se a vida está baixa (círculo vermelho detectado)
        
        Returns:
            True se vida baixa detectada
        """
        targets = self.detect_targets()
        low_health_indicators = [t for t in targets if t.type == 'low_health']
        
        return len(low_health_indicators) > 0
    
    def analyze_screenshot_for_debug(self, screenshot_path: str = "screenshots/scriptbot.png"):
        """Analisar screenshot para debug e calibração"""
        try:
            # Carregar imagem
            image = cv2.imread(screenshot_path)
            if image is None:
                logger.error(f"Não foi possível carregar {screenshot_path}")
                return
            
            logger.info(f"Analisando {screenshot_path}...")
            
            # Detectar alvos
            targets = self.detect_targets(image)
            
            # Criar imagem de debug
            debug_image = image.copy()
            
            for target in targets:
                x, y = target.position
                radius = target.size
                
                # Cor do círculo baseada no tipo
                color_map = {
                    'enemy_champion': (0, 255, 0),    # Verde
                    'enemy_minion': (255, 255, 255),  # Branco
                    'low_health': (0, 0, 255)         # Vermelho
                }
                
                color = color_map.get(target.type, (128, 128, 128))
                
                # Desenhar círculo
                cv2.circle(debug_image, (x, y), radius, color, 2)
                
                # Adicionar texto
                text = f"{target.type[:6]} {target.confidence:.2f}"
                cv2.putText(debug_image, text, (x - 30, y - radius - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Salvar imagem de debug
            debug_path = "screenshots/visual_detection_debug.png"
            cv2.imwrite(debug_path, debug_image)
            
            logger.info(f"Debug salvo em: {debug_path}")
            logger.info(f"Alvos detectados: {len(targets)}")
            
            for target in targets:
                logger.info(f"  {target.type}: {target.position} (conf: {target.confidence:.2f})")
            
            return targets
            
        except Exception as e:
            logger.error(f"Erro na análise de debug: {e}")
            return []

# Instância global
visual_detector = VisualDetector()
