🤖 LEAGUE OF LEGENDS BOT - GUIA RÁPIDO
==========================================

📋 PASSO A PASSO:

1. 📁 PREPARAR IMAGEM:
   - Tire um screenshot do seu minimapa no jogo
   - Salve como: screenshots/mapa.png
   - IMPORTANTE: A imagem deve mostrar pontos azuis (aliados) e vermelhos (inimigos)

2. 🚀 EXECUTAR BOT:
   - Windows: Clique duas vezes em "EXECUTAR_BOT.bat"
   - Outros: Execute "python run_bot.py"

3. ⚙️ CONFIGURAR:
   - Escolha opção 1 (Executar bot)
   - Selecione sua lane preferida (Mid recomendado)
   - Escolha estilo de jogo (Balanceado recomendado)

4. 🎮 JOGAR:
   - Abra League of Legends
   - Entre em uma partida
   - Configure em TELA CHEIA (pressione F11)
   - Aguarde o bot detectar automaticamente

5. ✅ FUNCIONAMENTO:
   O bot irá automaticamente:
   - Seguir seus minions aliados (pontos azuis)
   - Atacar campeões inimigos (pontos vermelhos grandes)
   - Recuar quando poucos minions aliados
   - Aguardar novas waves de minions

📊 STATUS EM TEMPO REAL:
🤖 Frame 1234 | 🔵 6 | ⚔️ 2 | 🌊 FOLLOWING | Ações: 45

🔵 = Minions aliados detectados
⚔️ = Campeões inimigos detectados  
🌊 = Estado atual do bot

🔧 PROBLEMAS COMUNS:

❌ "Python não encontrado"
   → Instale Python 3.8+ em python.org

❌ "Arquivo mapa.png não encontrado"
   → Coloque sua imagem do minimapa em screenshots/mapa.png

❌ "Bot não detecta o jogo"
   → Certifique-se que o LoL está em tela cheia (F11)
   → Use resolução 1920x1080

❌ "Não ataca inimigos"
   → Teste com opção 3 no launcher
   → Verifique se há minions aliados suficientes

❌ "Movimento incorreto"
   → Verifique se a imagem mapa.png está correta
   → Teste diferentes configurações

🎯 ESTRATÉGIA DO BOT:

PRIORIDADE 1: Vida baixa → Recua para torre
PRIORIDADE 2: ≤2 minions aliados → Recua e aguarda wave
PRIORIDADE 3: Campeão inimigo + ≥3 minions → Ataca campeão
PRIORIDADE 4: Torre próxima + ≥3 minions → Ataca torre
PRIORIDADE 5: Seguir minions aliados

⚠️ IMPORTANTE:
- O bot funciona melhor em partidas contra bots
- Mantenha sempre o jogo em tela cheia
- Não mova o mouse durante o funcionamento
- Use por sua conta e risco

📞 SUPORTE:
- Teste com: python test_advanced_minimap.py
- Verifique logs na pasta logs/
- Leia README.md para mais detalhes

🎉 DIVIRTA-SE!
O bot está pronto para jogar automaticamente seguindo a estratégia de waves e priorizando campeões inimigos!
