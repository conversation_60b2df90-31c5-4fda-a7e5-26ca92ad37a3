"""
Script principal para executar o bot - Instalação e execução automática
"""
import subprocess
import sys
import os
from pathlib import Path

def check_python():
    """Verificar versão do Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ é necessário")
        print(f"   Versão atual: {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor} detectado")
    return True

def install_dependencies():
    """Instalar dependências automaticamente"""
    print("📦 Instalando dependências...")
    
    try:
        # Tentar pip install
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Dependências instaladas com sucesso!")
            return True
        else:
            print("❌ Erro na instalação:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout na instalação - tente manualmente")
        return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def check_files():
    """Verificar arquivos essenciais"""
    essential_files = [
        'bot_intelligent.py',
        'config.py',
        'requirements.txt',
        'game/advanced_minimap.py',
        'game/combat_system.py',
        'game/lol_detector.py',
        'utils/logger.py',
        'utils/input_utils.py',
        'screenshots/mapa.png'
    ]
    
    missing_files = []
    
    for file in essential_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Arquivos essenciais faltando:")
        for file in missing_files:
            print(f"   📄 {file}")
        
        if 'screenshots/mapa.png' in missing_files:
            print("\n⚠️ IMPORTANTE: Coloque sua imagem do minimapa em screenshots/mapa.png")
        
        return False
    
    print("✅ Todos os arquivos essenciais encontrados")
    return True

def run_bot():
    """Executar o bot principal"""
    print("🚀 Iniciando bot...")
    
    try:
        # Executar bot_intelligent.py
        subprocess.run([sys.executable, "bot_intelligent.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao executar bot: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏸️ Bot interrompido pelo usuário")
        return True
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False
    
    return True

def show_menu():
    """Mostrar menu principal"""
    print("\n" + "="*50)
    print("🤖 League of Legends Bot - Sistema Avançado")
    print("="*50)
    print("1. 🚀 Executar bot (recomendado)")
    print("2. 📦 Instalar dependências")
    print("3. 🧪 Testar sistema")
    print("4. 📋 Verificar arquivos")
    print("5. 📖 Mostrar instruções")
    print("6. ❌ Sair")
    print("="*50)

def show_instructions():
    """Mostrar instruções de uso"""
    print("\n📖 INSTRUÇÕES DE USO:")
    print("="*30)
    print("1. ✅ Certifique-se que o arquivo screenshots/mapa.png existe")
    print("2. ✅ Abra o League of Legends")
    print("3. ✅ Entre em uma partida")
    print("4. ✅ Configure o jogo em TELA CHEIA (F11)")
    print("5. ✅ Execute o bot (opção 1)")
    print("6. ✅ Siga as instruções na tela")
    print("\n🎯 O bot irá:")
    print("   • Seguir minions aliados (pontos azuis)")
    print("   • Atacar campeões inimigos (pontos vermelhos)")
    print("   • Recuar quando poucos minions")
    print("   • Aguardar novas waves automaticamente")

def run_test():
    """Executar teste do sistema"""
    print("🧪 Executando teste...")
    
    try:
        subprocess.run([sys.executable, "test_advanced_minimap.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro no teste: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏸️ Teste interrompido")
        return True
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False
    
    return True

def main():
    """Função principal"""
    print("🤖 League of Legends Bot - Launcher")
    print("Versão: Sistema Avançado de Minimapa")
    print("-" * 40)
    
    # Verificar Python
    if not check_python():
        input("Pressione Enter para sair...")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEscolha uma opção (1-6): ").strip()
            
            if choice == "1":
                # Verificar arquivos antes de executar
                if not check_files():
                    print("\n⚠️ Corrija os problemas acima antes de continuar")
                    input("Pressione Enter para continuar...")
                    continue
                
                # Executar bot
                run_bot()
                
            elif choice == "2":
                install_dependencies()
                input("Pressione Enter para continuar...")
                
            elif choice == "3":
                if not check_files():
                    print("\n⚠️ Corrija os problemas acima antes de testar")
                    input("Pressione Enter para continuar...")
                    continue
                
                run_test()
                input("Pressione Enter para continuar...")
                
            elif choice == "4":
                check_files()
                input("Pressione Enter para continuar...")
                
            elif choice == "5":
                show_instructions()
                input("Pressione Enter para continuar...")
                
            elif choice == "6":
                print("👋 Saindo...")
                break
                
            else:
                print("❌ Opção inválida!")
                
        except KeyboardInterrupt:
            print("\n👋 Saindo...")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")
            input("Pressione Enter para continuar...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        input("Pressione Enter para sair...")
