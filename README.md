# 🤖 League of Legends Bot - Sistema Avançado

Bot inteligente que joga League of Legends automaticamente seguindo minions aliados e atacando inimigos.

## ⚡ Execução Rápida

### **Windows (Recomendado):**
```
1. Clique duas vezes em: EXECUTAR_BOT.bat
2. <PERSON><PERSON> as instruções na tela
```

### **Manual:**
```bash
# 1. Instalar dependências
pip install -r requirements.txt

# 2. Executar launcher
python run_bot.py
```

## 📋 Pré-requisitos

- ✅ **Python 3.8+** instalado
- ✅ **League of Legends** instalado
- ✅ **Imagem do minimapa** em `screenshots/mapa.png`

## 🎮 Como Usar

1. **Execute o launcher:**
   - Windows: Clique em `EXECUTAR_BOT.bat`
   - Manual: `python run_bot.py`

2. **<PERSON>ga as instruções:**
   - Escolha opção 1 (Executar bot)
   - Configure sua lane preferida
   - Entre em uma partida do LoL
   - Configure em tela cheia (F11)

3. **O bot irá automaticamente:**
   - Seguir seus minions aliados (pontos azuis)
   - Atacar campeões inimigos (pontos vermelhos)
   - Recuar quando poucos minions
   - Aguardar novas waves

## 🎯 Funcionalidades

- ✅ **Segue minions aliados** (pontos azuis no minimapa)
- ✅ **Ataca campeões inimigos** (pontos vermelhos grandes)
- ✅ **Ignora minions inimigos** (foca em campeões)
- ✅ **Ataca torres** quando tem suporte de minions
- ✅ **Recua automaticamente** quando ≤2 minions aliados
- ✅ **Aguarda novas waves** (30s cooldown)
- ✅ **Fica sempre próximo** dos minions azuis

## 📊 Status em Tempo Real

```
🤖 Frame 1234 | 🔵 6 | ⚔️ 2 | 🌊 FOLLOWING | Ações: 45
```

- **🔵** = Minions aliados (azuis)
- **⚔️** = Campeões inimigos (vermelhos)
- **🌊** = Estado atual (FOLLOWING/ATTACKING/RETREATING)

## 🔧 Solução de Problemas

| Problema | Solução |
|----------|---------|
| Bot não inicia | Execute `EXECUTAR_BOT.bat` como administrador |
| Não detecta jogo | LoL em tela cheia (F11) + resolução 1920x1080 |
| Não ataca inimigos | Verifique se `mapa.png` existe em screenshots/ |
| Movimento incorreto | Teste com opção 3 no launcher |

## 📁 Arquivos Essenciais

```
📁 Bot-Lol/
├── 🚀 EXECUTAR_BOT.bat           # Launcher Windows
├── 🐍 run_bot.py                 # Launcher Python
├── 🐍 bot_intelligent.py         # Bot principal
├── 📁 game/                      # Sistemas do jogo
├── 📁 utils/                     # Utilitários
└── 📁 screenshots/
    └── 🖼️ mapa.png               # SUA IMAGEM DO MINIMAPA
```

## ⚠️ IMPORTANTE

**Coloque sua imagem do minimapa em `screenshots/mapa.png` antes de usar o bot!**
