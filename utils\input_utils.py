"""
Utilitários para controle de mouse e teclado
"""
import pyautogui
import random
import time
import math
from typing import Tuple, List
import numpy as np

from .logger import logger
from config import config

class InputController:
    """Classe para controle humanizado de mouse e teclado"""
    
    def __init__(self):
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0

        # Configurações para evitar mudança de foco
        self.prevent_focus_change = True
    
    def humanized_delay(self, min_delay: float = None, max_delay: float = None):
        """Delay aleatório humanizado"""
        if not config.RANDOM_DELAYS:
            return
            
        min_d = min_delay or config.MIN_DELAY
        max_d = max_delay or config.MAX_DELAY
        delay = random.uniform(min_d, max_d)
        time.sleep(delay)
    
    def move_mouse_humanized(self, x: int, y: int, duration: float = None):
        """
        Mover mouse de forma humanizada com curva
        
        Args:
            x, y: Coordenadas de destino
            duration: Duração do movimento
        """
        if not config.HUMANIZE_MOVEMENTS:
            pyautogui.moveTo(x, y)
            return
        
        current_x, current_y = pyautogui.position()
        
        # Calcular duração baseada na distância
        if duration is None:
            distance = math.sqrt((x - current_x)**2 + (y - current_y)**2)
            duration = max(0.1, min(1.0, distance / 1000))
        
        # Gerar pontos de curva Bézier
        points = self._generate_bezier_curve(current_x, current_y, x, y, duration)
        
        # Mover através dos pontos
        for point_x, point_y in points:
            pyautogui.moveTo(int(point_x), int(point_y))
            time.sleep(0.01)
    
    def _generate_bezier_curve(self, start_x: int, start_y: int, 
                              end_x: int, end_y: int, duration: float) -> List[Tuple[float, float]]:
        """Gerar curva Bézier para movimento natural do mouse"""
        
        # Pontos de controle aleatórios
        mid_x = (start_x + end_x) / 2 + random.randint(-50, 50)
        mid_y = (start_y + end_y) / 2 + random.randint(-50, 50)
        
        points = []
        steps = int(duration * 100)  # 100 pontos por segundo
        
        for i in range(steps + 1):
            t = i / steps
            
            # Curva Bézier quadrática
            x = (1-t)**2 * start_x + 2*(1-t)*t * mid_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * mid_y + t**2 * end_y
            
            points.append((x, y))
        
        return points
    
    def click_at(self, x: int, y: int, button: str = 'left', clicks: int = 1):
        """
        Clicar em coordenadas específicas
        
        Args:
            x, y: Coordenadas
            button: Botão do mouse ('left', 'right', 'middle')
            clicks: Número de cliques
        """
        try:
            self.move_mouse_humanized(x, y)
            self.humanized_delay(0.1, 0.3)
            
            pyautogui.click(x, y, clicks=clicks, button=button)
            logger.debug(f"Clique em ({x}, {y}) - botão: {button}")
            
            self.humanized_delay()
            
        except Exception as e:
            logger.error(f"Erro ao clicar em ({x}, {y}): {e}")
    
    def right_click_at(self, x: int, y: int):
        """Clique direito em coordenadas sem mudança de foco"""
        if self.prevent_focus_change:
            # Movimento e clique mais rápidos para evitar mudança de foco
            try:
                pyautogui.moveTo(x, y, duration=0.05)
                time.sleep(0.01)
                pyautogui.rightClick(x, y)
                logger.debug(f"Clique direito rápido em ({x}, {y})")
            except Exception as e:
                logger.error(f"Erro no clique direito rápido: {e}")
        else:
            self.click_at(x, y, button='right')
    
    def double_click_at(self, x: int, y: int):
        """Duplo clique em coordenadas"""
        self.click_at(x, y, clicks=2)
    
    def drag_to(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0):
        """
        Arrastar de um ponto para outro
        
        Args:
            start_x, start_y: Coordenadas iniciais
            end_x, end_y: Coordenadas finais
            duration: Duração do arraste
        """
        try:
            self.move_mouse_humanized(start_x, start_y)
            self.humanized_delay(0.1, 0.2)
            
            pyautogui.dragTo(end_x, end_y, duration=duration, button='left')
            logger.debug(f"Arraste de ({start_x}, {start_y}) para ({end_x}, {end_y})")
            
            self.humanized_delay()
            
        except Exception as e:
            logger.error(f"Erro ao arrastar: {e}")
    
    def type_text(self, text: str, interval: float = None):
        """
        Digitar texto com intervalo humanizado
        
        Args:
            text: Texto para digitar
            interval: Intervalo entre caracteres
        """
        try:
            if interval is None:
                interval = random.uniform(0.05, 0.15)
            
            pyautogui.typewrite(text, interval=interval)
            logger.debug(f"Texto digitado: {text}")
            
            self.humanized_delay()
            
        except Exception as e:
            logger.error(f"Erro ao digitar texto '{text}': {e}")
    
    def press_key(self, key: str, presses: int = 1):
        """
        Pressionar tecla
        
        Args:
            key: Nome da tecla
            presses: Número de vezes para pressionar
        """
        try:
            for _ in range(presses):
                pyautogui.press(key)
                self.humanized_delay(0.05, 0.1)
            
            logger.debug(f"Tecla pressionada: {key} ({presses}x)")
            
        except Exception as e:
            logger.error(f"Erro ao pressionar tecla '{key}': {e}")
    
    def key_combination(self, *keys):
        """
        Pressionar combinação de teclas
        
        Args:
            keys: Teclas para pressionar simultaneamente
        """
        try:
            pyautogui.hotkey(*keys)
            logger.debug(f"Combinação de teclas: {'+'.join(keys)}")
            
            self.humanized_delay()
            
        except Exception as e:
            logger.error(f"Erro na combinação de teclas {keys}: {e}")

# Instância global
input_controller = InputController()
