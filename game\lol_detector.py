"""
Detector específico para League of Legends
Detecta se o jogo está aberto e captura a tela real
"""
import cv2
import numpy as np
import pyautogui
import psutil
import win32gui
import win32con
import time
from typing import Optional, Tuple, Dict
from utils.logger import logger

class LoLDetector:
    """Detector específico para League of Legends"""
    
    def __init__(self):
        # Configurar pyautogui
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.05
        
        # Informações da janela do LoL
        self.lol_window = None
        self.lol_rect = None
        self.last_window_check = 0
        self.game_region = None
        
        # Nomes de processos do LoL
        self.lol_processes = [
            'League of Legends.exe',
            'LeagueClient.exe',
            'RiotClientServices.exe'
        ]
        
        # Títulos de janela do LoL (mais específicos)
        self.lol_window_titles = [
            'league of legends (tm) client'
        ]

        # Títulos que devem ser IGNORADOS
        self.ignore_titles = [
            'visual studio code',
            'vscode',
            'bot-lol',
            'scriptbot.png'
        ]
    
    def is_lol_running(self) -> bool:
        """Verificar se League of Legends está rodando"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] in self.lol_processes:
                    logger.debug(f"Processo LoL encontrado: {proc.info['name']}")
                    return True
            
            logger.debug("Nenhum processo LoL encontrado")
            return False
            
        except Exception as e:
            logger.error(f"Erro ao verificar processos LoL: {e}")
            return False
    
    def find_lol_window(self) -> bool:
        """Encontrar janela do League of Legends"""
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd).lower()

                    # Ignorar janelas específicas
                    if any(ignore in window_title for ignore in self.ignore_titles):
                        return True

                    # Procurar por League of Legends
                    if any(keyword in window_title for keyword in self.lol_window_titles):
                        rect = win32gui.GetWindowRect(hwnd)
                        # Filtrar janelas muito pequenas
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        if width > 800 and height > 600:
                            windows.append((hwnd, window_title, rect))

                    # Também procurar por "League of Legends" sem ser específico demais
                    elif "league of legends" in window_title and "visual studio" not in window_title:
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        if width > 1000 and height > 700:  # Janela maior para o jogo
                            windows.append((hwnd, window_title, rect))

                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            if windows:
                # Pegar a maior janela (provavelmente o jogo)
                largest_window = max(windows, key=lambda w: (w[2][2] - w[2][0]) * (w[2][3] - w[2][1]))
                
                self.lol_window = largest_window[0]
                self.lol_rect = largest_window[2]
                
                logger.info(f"Janela LoL encontrada: {largest_window[1]}")
                logger.info(f"Posição: {self.lol_rect}")
                
                # Calcular região do jogo (sem bordas)
                self._calculate_game_region()
                
                return True
            else:
                logger.warning("Janela do League of Legends não encontrada")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao procurar janela LoL: {e}")
            return False
    
    def _calculate_game_region(self):
        """Calcular região do jogo sem bordas da janela"""
        if not self.lol_rect:
            return
        
        x, y, x2, y2 = self.lol_rect
        
        # Ajustes para remover bordas da janela
        border_left = 8
        border_right = 8
        border_top = 30  # Barra de título
        border_bottom = 8
        
        self.game_region = (
            x + border_left,
            y + border_top,
            (x2 - x) - border_left - border_right,
            (y2 - y) - border_top - border_bottom
        )
        
        logger.debug(f"Região do jogo calculada: {self.game_region}")
    
    def capture_lol_screen(self) -> Optional[np.ndarray]:
        """Capturar tela específica do League of Legends"""
        try:
            # Verificar se precisa atualizar janela
            current_time = time.time()
            if current_time - self.last_window_check > 5:  # A cada 5 segundos
                if not self.is_lol_running():
                    logger.warning("LoL não está rodando")
                    return None
                
                self.find_lol_window()
                self.last_window_check = current_time
            
            if not self.game_region:
                if not self.find_lol_window():
                    return None
            
            # Capturar região do jogo
            screenshot = pyautogui.screenshot(region=self.game_region)
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            return screenshot_cv
            
        except Exception as e:
            logger.error(f"Erro ao capturar tela LoL: {e}")
            return None
    
    def is_fullscreen(self) -> bool:
        """Verificar se a janela está em tela cheia"""
        try:
            if not self.lol_window or not self.lol_rect:
                return False

            # Obter resolução da tela
            import win32api
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)

            x, y, x2, y2 = self.lol_rect
            window_width = x2 - x
            window_height = y2 - y

            # Considerar tela cheia se ocupar pelo menos 90% da tela
            width_ratio = window_width / screen_width
            height_ratio = window_height / screen_height

            is_fullscreen = width_ratio >= 0.9 and height_ratio >= 0.9

            logger.debug(f"Resolução tela: {screen_width}x{screen_height}")
            logger.debug(f"Tamanho janela: {window_width}x{window_height}")
            logger.debug(f"Ratios: {width_ratio:.2f}x{height_ratio:.2f}")
            logger.debug(f"Tela cheia: {is_fullscreen}")

            return is_fullscreen

        except Exception as e:
            logger.error(f"Erro ao verificar tela cheia: {e}")
            return False

    def focus_lol_window(self, force: bool = False) -> bool:
        """Focar na janela do League of Legends sem Alt+Tab"""
        try:
            if not self.lol_window:
                if not self.find_lol_window():
                    return False

            # Verificar se já está em foco
            try:
                current_window = win32gui.GetForegroundWindow()
                if current_window == self.lol_window and not force:
                    logger.debug("Janela LoL já está em foco")
                    return True
            except:
                pass

            # Método suave para focar sem Alt+Tab
            try:
                # Primeiro, restaurar se minimizada
                win32gui.ShowWindow(self.lol_window, win32con.SW_RESTORE)
                time.sleep(0.1)

                # Trazer para frente suavemente
                win32gui.SetForegroundWindow(self.lol_window)
                time.sleep(0.1)

                # Garantir que está ativa
                win32gui.SetActiveWindow(self.lol_window)

                logger.debug("Janela LoL focada suavemente")
                return True

            except Exception as focus_error:
                logger.warning(f"Método suave falhou: {focus_error}")
                # Fallback mais direto
                win32gui.SetForegroundWindow(self.lol_window)
                return True

        except Exception as e:
            logger.error(f"Erro ao focar janela LoL: {e}")
            return False
    
    def get_lol_status(self) -> Dict:
        """Obter status completo do League of Legends"""
        try:
            status = {
                'running': self.is_lol_running(),
                'window_found': False,
                'window_focused': False,
                'game_region': None,
                'screen_captured': False
            }
            
            if status['running']:
                status['window_found'] = self.find_lol_window()
                
                if status['window_found']:
                    status['game_region'] = self.game_region
                    
                    # Testar captura de tela
                    test_capture = self.capture_lol_screen()
                    status['screen_captured'] = test_capture is not None
                    
                    # Verificar se janela está em foco
                    try:
                        foreground_window = win32gui.GetForegroundWindow()
                        status['window_focused'] = foreground_window == self.lol_window
                    except:
                        status['window_focused'] = False
            
            return status
            
        except Exception as e:
            logger.error(f"Erro ao obter status LoL: {e}")
            return {'running': False, 'error': str(e)}
    
    def wait_for_lol_fullscreen(self, timeout: int = 120) -> bool:
        """Aguardar League of Legends estar em tela cheia e pronto"""
        logger.info(f"🎮 Aguardando League of Legends em tela cheia por {timeout} segundos...")
        logger.info("⚠️ Certifique-se que o jogo está em TELA CHEIA (F11 ou configurações)")

        start_time = time.time()
        last_status_time = 0

        while time.time() - start_time < timeout:
            current_time = time.time()

            # Mostrar status a cada 5 segundos
            if current_time - last_status_time >= 5:
                elapsed = int(current_time - start_time)
                remaining = timeout - elapsed
                logger.info(f"⏱️ Aguardando... {elapsed}s/{timeout}s (restam {remaining}s)")
                last_status_time = current_time

            # Verificar status completo
            status = self.get_lol_status()

            if not status['running']:
                logger.warning("❌ LoL não está rodando - aguardando...")
                time.sleep(3)
                continue

            if not status['window_found']:
                logger.warning("❌ Janela não encontrada - aguardando...")
                time.sleep(3)
                continue

            if not status['screen_captured']:
                logger.warning("❌ Não foi possível capturar tela - aguardando...")
                time.sleep(3)
                continue

            # Verificar se está em tela cheia
            if not self.is_fullscreen():
                logger.warning("❌ Jogo não está em tela cheia - aguardando...")
                logger.info("💡 Pressione F11 ou configure para tela cheia")
                time.sleep(3)
                continue

            # Verificar se está em foco
            if not status.get('window_focused', False):
                logger.info("🎯 Focando janela do jogo...")
                self.focus_lol_window()
                time.sleep(2)
                continue

            # Tudo pronto!
            logger.info("✅ League of Legends está em tela cheia e pronto!")
            logger.info("🎮 Iniciando automação em 3 segundos...")

            # Countdown final
            for i in range(3, 0, -1):
                logger.info(f"⏰ Iniciando em {i}...")
                time.sleep(1)

            return True

        logger.error("⏰ Timeout aguardando League of Legends em tela cheia")
        return False

    def wait_for_lol(self, timeout: int = 60) -> bool:
        """Aguardar League of Legends estar pronto (método antigo)"""
        logger.info(f"Aguardando League of Legends por {timeout} segundos...")

        start_time = time.time()

        while time.time() - start_time < timeout:
            status = self.get_lol_status()

            if status['running'] and status['window_found'] and status['screen_captured']:
                logger.info("League of Legends detectado e pronto!")
                return True

            logger.debug(f"Status LoL: {status}")
            time.sleep(2)

        logger.error("Timeout aguardando League of Legends")
        return False
    
    def ensure_lol_ready(self) -> bool:
        """Garantir que LoL está pronto para automação"""
        try:
            # Verificar se está rodando
            if not self.is_lol_running():
                logger.error("League of Legends não está rodando!")
                return False
            
            # Encontrar janela
            if not self.find_lol_window():
                logger.error("Não foi possível encontrar a janela do LoL!")
                return False
            
            # Focar janela
            if not self.focus_lol_window():
                logger.warning("Não foi possível focar a janela do LoL")
            
            # Testar captura
            test_capture = self.capture_lol_screen()
            if test_capture is None:
                logger.error("Não foi possível capturar a tela do LoL!")
                return False
            
            logger.info("League of Legends está pronto para automação!")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao preparar LoL: {e}")
            return False

# Instância global
lol_detector = LoLDetector()
