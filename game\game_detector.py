"""
Detector de estados do cliente League of Legends
Detecta diferentes telas e estados do cliente para automação
"""
import time
import cv2
import numpy as np
from enum import Enum
from typing import Optional
from utils.logger import logger
from game.lol_detector import lol_detector


class GameState(Enum):
    """Estados do cliente League of Legends"""
    CLIENT_MAIN_MENU = "client_main_menu"
    CHAMPION_SELECT = "champion_select"
    LOADING_SCREEN = "loading_screen"
    IN_GAME = "in_game"
    POST_GAME = "post_game"
    QUEUE = "queue"
    UNKNOWN = "unknown"


class GameDetector:
    """Detector de estados do cliente LoL"""
    
    def __init__(self):
        self.current_state = GameState.UNKNOWN
        self.last_state_check = 0
        self.state_check_interval = 1.0  # Verificar estado a cada 1 segundo
        
        # Templates ou características para detectar cada estado
        # Por enquanto, usaremos detecção simples baseada em tempo
        logger.info("🔍 Detector de estados do cliente inicializado")
    
    def detect_current_state(self) -> GameState:
        """
        Detectar o estado atual do cliente
        
        Returns:
            Estado atual detectado
        """
        try:
            # Capturar tela atual
            screenshot = lol_detector.capture_lol_screen()
            if screenshot is None:
                return GameState.UNKNOWN
            
            # Por enquanto, implementação simples
            # Em uma implementação completa, analisaríamos a imagem para detectar elementos específicos
            
            # Detectar baseado em características da tela
            state = self._analyze_screenshot(screenshot)
            
            if state != self.current_state:
                logger.info(f"🔄 Estado mudou: {self.current_state.value} → {state.value}")
                self.current_state = state
            
            return state
            
        except Exception as e:
            logger.error(f"Erro ao detectar estado: {e}")
            return GameState.UNKNOWN
    
    def _analyze_screenshot(self, screenshot: np.ndarray) -> GameState:
        """
        Analisar screenshot para determinar estado
        
        Args:
            screenshot: Imagem capturada da tela
            
        Returns:
            Estado detectado
        """
        try:
            # Converter para escala de cinza para análise
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape
            
            # Implementação simples baseada em características gerais
            # Em uma implementação completa, usaríamos template matching ou OCR
            
            # Verificar se há muito texto (possível menu principal)
            text_density = self._calculate_text_density(gray)
            
            # Verificar cores predominantes
            color_analysis = self._analyze_colors(screenshot)
            
            # Lógica de detecção simples
            if text_density > 0.3:
                return GameState.CLIENT_MAIN_MENU
            elif color_analysis.get('dark_background', False):
                return GameState.CHAMPION_SELECT
            elif color_analysis.get('loading_indicators', False):
                return GameState.LOADING_SCREEN
            else:
                return GameState.IN_GAME
                
        except Exception as e:
            logger.error(f"Erro ao analisar screenshot: {e}")
            return GameState.UNKNOWN
    
    def _calculate_text_density(self, gray_image: np.ndarray) -> float:
        """Calcular densidade de texto na imagem"""
        try:
            # Detectar bordas que podem indicar texto
            edges = cv2.Canny(gray_image, 50, 150)
            text_pixels = np.sum(edges > 0)
            total_pixels = gray_image.shape[0] * gray_image.shape[1]
            return text_pixels / total_pixels
        except:
            return 0.0
    
    def _analyze_colors(self, image: np.ndarray) -> dict:
        """Analisar cores predominantes na imagem"""
        try:
            # Converter para HSV
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Calcular histograma
            hist_h = cv2.calcHist([hsv], [0], None, [180], [0, 180])
            hist_s = cv2.calcHist([hsv], [1], None, [256], [0, 256])
            hist_v = cv2.calcHist([hsv], [2], None, [256], [0, 256])
            
            # Análise simples
            dark_pixels = np.sum(hist_v[:50])  # Pixels escuros
            total_pixels = image.shape[0] * image.shape[1]
            
            return {
                'dark_background': dark_pixels / total_pixels > 0.5,
                'loading_indicators': False  # Implementar detecção específica
            }
        except:
            return {'dark_background': False, 'loading_indicators': False}
    
    def wait_for_state(self, target_state: GameState, timeout: int = 30) -> bool:
        """
        Aguardar um estado específico
        
        Args:
            target_state: Estado alvo a aguardar
            timeout: Timeout em segundos
            
        Returns:
            True se o estado foi alcançado, False se timeout
        """
        logger.info(f"⏳ Aguardando estado: {target_state.value} (timeout: {timeout}s)")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_state = self.detect_current_state()
            
            if current_state == target_state:
                logger.info(f"✅ Estado {target_state.value} alcançado!")
                return True
            
            # Log periódico
            elapsed = time.time() - start_time
            if elapsed % 5 == 0:  # A cada 5 segundos
                logger.info(f"⏳ Aguardando {target_state.value}... ({elapsed:.0f}s/{timeout}s)")
            
            time.sleep(1)
        
        logger.warning(f"⏰ Timeout aguardando estado {target_state.value}")
        return False
    
    def get_current_state(self) -> GameState:
        """Obter estado atual (com cache)"""
        current_time = time.time()
        
        if current_time - self.last_state_check > self.state_check_interval:
            self.current_state = self.detect_current_state()
            self.last_state_check = current_time
        
        return self.current_state
    
    def is_in_state(self, state: GameState) -> bool:
        """Verificar se está em um estado específico"""
        return self.get_current_state() == state
    
    def wait_for_any_state(self, states: list, timeout: int = 30) -> Optional[GameState]:
        """
        Aguardar qualquer um dos estados especificados
        
        Args:
            states: Lista de estados a aguardar
            timeout: Timeout em segundos
            
        Returns:
            Estado alcançado ou None se timeout
        """
        logger.info(f"⏳ Aguardando qualquer estado: {[s.value for s in states]}")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_state = self.detect_current_state()
            
            if current_state in states:
                logger.info(f"✅ Estado {current_state.value} alcançado!")
                return current_state
            
            time.sleep(1)
        
        logger.warning(f"⏰ Timeout aguardando estados: {[s.value for s in states]}")
        return None


# Instância global
game_detector = GameDetector()
