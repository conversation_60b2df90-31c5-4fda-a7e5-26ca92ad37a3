@echo off
title League of Legends Bot - Sistema Avancado
color 0A

echo.
echo ========================================
echo   League of Legends Bot - Launcher
echo ========================================
echo.

REM Verificar se Python esta instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python nao encontrado!
    echo    Instale Python 3.8+ em python.org
    pause
    exit /b 1
)

echo ✅ Python encontrado
echo.

REM Verificar se arquivo principal existe
if not exist "run_bot.py" (
    echo ❌ Arquivo run_bot.py nao encontrado!
    pause
    exit /b 1
)

echo 🚀 Iniciando launcher...
echo.

REM Executar launcher Python
python run_bot.py

echo.
echo 👋 Bot finalizado
pause
