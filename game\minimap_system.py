"""
Sistema de minimapa básico para League of Legends
Detecta inimigos e fornece informações de posicionamento
"""
import time
import cv2
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from utils.logger import logger
from game.lol_detector import lol_detector


class Lane(Enum):
    """Lanes do jogo"""
    TOP = "top"
    MID = "mid"
    BOT = "bot"
    JUNGLE = "jungle"


@dataclass
class MinimapEnemy:
    """Inimigo detectado no minimapa"""
    position: Tuple[int, int]
    lane: Lane
    is_champion: bool
    distance_to_player: float
    last_seen: float
    confidence: float = 0.8


class MinimapSystem:
    """Sistema básico de minimapa"""
    
    def __init__(self):
        # Região do minimapa na tela (ajustar conforme resolução)
        self.minimap_region = (1620, 780, 300, 300)  # x, y, width, height
        
        # Posição do jogador no minimapa (centro aproximado)
        self.player_position = (150, 150)
        
        # Lane atual
        self.current_lane = Lane.MID
        
        # Cache de inimigos detectados
        self.detected_enemies = []
        self.last_detection_time = 0
        self.detection_interval = 1.0  # Detectar a cada 1 segundo
        
        # Configurações de detecção
        self.enemy_color_ranges = {
            'red': {
                'lower1': np.array([0, 50, 50]),
                'upper1': np.array([10, 255, 255]),
                'lower2': np.array([170, 50, 50]),
                'upper2': np.array([180, 255, 255])
            }
        }
        
        logger.info("🗺️ Sistema básico de minimapa inicializado")
    
    def capture_minimap(self) -> Optional[np.ndarray]:
        """Capturar região do minimapa"""
        try:
            # Capturar tela completa
            screenshot = lol_detector.capture_lol_screen()
            if screenshot is None:
                return None
            
            # Extrair região do minimapa
            x, y, w, h = self.minimap_region
            minimap = screenshot[y:y+h, x:x+w]
            
            return minimap
            
        except Exception as e:
            logger.error(f"Erro ao capturar minimapa: {e}")
            return None
    
    def detect_enemies(self, minimap: np.ndarray = None) -> List[MinimapEnemy]:
        """Detectar inimigos no minimapa"""
        try:
            if minimap is None:
                minimap = self.capture_minimap()
                if minimap is None:
                    return []
            
            # Converter para HSV
            hsv = cv2.cvtColor(minimap, cv2.COLOR_BGR2HSV)
            
            enemies = []
            current_time = time.time()
            
            # Detectar pontos vermelhos (inimigos)
            red_mask1 = cv2.inRange(hsv, self.enemy_color_ranges['red']['lower1'], 
                                   self.enemy_color_ranges['red']['upper1'])
            red_mask2 = cv2.inRange(hsv, self.enemy_color_ranges['red']['lower2'], 
                                   self.enemy_color_ranges['red']['upper2'])
            red_mask = cv2.bitwise_or(red_mask1, red_mask2)
            
            # Encontrar contornos
            contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < 5:  # Muito pequeno
                    continue
                
                # Calcular centro
                M = cv2.moments(contour)
                if M["m00"] == 0:
                    continue
                
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # Determinar se é campeão baseado no tamanho
                is_champion = area > 30
                
                # Calcular distância do jogador
                distance = self._calculate_distance((cx, cy), self.player_position)
                
                # Determinar lane baseado na posição
                lane = self._determine_lane((cx, cy))
                
                enemy = MinimapEnemy(
                    position=(cx, cy),
                    lane=lane,
                    is_champion=is_champion,
                    distance_to_player=distance,
                    last_seen=current_time,
                    confidence=0.8 if is_champion else 0.6
                )
                
                enemies.append(enemy)
            
            return enemies
            
        except Exception as e:
            logger.error(f"Erro ao detectar inimigos no minimapa: {e}")
            return []
    
    def get_priority_targets(self) -> List[MinimapEnemy]:
        """Obter alvos prioritários do minimapa"""
        try:
            current_time = time.time()
            
            # Verificar se precisa atualizar detecção
            if current_time - self.last_detection_time > self.detection_interval:
                self.detected_enemies = self.detect_enemies()
                self.last_detection_time = current_time
            
            # Filtrar e priorizar
            priority_enemies = []
            
            for enemy in self.detected_enemies:
                # Priorizar campeões
                if enemy.is_champion:
                    priority_enemies.append(enemy)
            
            # Se não há campeões, incluir minions próximos
            if not priority_enemies:
                close_enemies = [e for e in self.detected_enemies if e.distance_to_player < 50]
                priority_enemies.extend(close_enemies)
            
            # Ordenar por prioridade (campeões primeiro, depois por distância)
            priority_enemies.sort(key=lambda e: (not e.is_champion, e.distance_to_player))
            
            return priority_enemies[:3]  # Máximo 3 alvos
            
        except Exception as e:
            logger.error(f"Erro ao obter alvos prioritários: {e}")
            return []
    
    def convert_minimap_to_screen(self, minimap_pos: Tuple[int, int]) -> Tuple[int, int]:
        """Converter posição do minimapa para coordenadas da tela"""
        try:
            # Proporção do minimapa para a tela do jogo
            # Assumindo que o minimapa representa toda a área do jogo
            minimap_x, minimap_y = minimap_pos
            
            # Normalizar posição no minimapa (0-1)
            norm_x = minimap_x / 300  # Largura do minimapa
            norm_y = minimap_y / 300  # Altura do minimapa
            
            # Converter para coordenadas da tela (área jogável)
            # Área jogável aproximada: 200-1720 x 100-900
            screen_x = int(200 + (norm_x * 1520))
            screen_y = int(100 + (norm_y * 800))
            
            # Garantir limites
            screen_x = max(200, min(1720, screen_x))
            screen_y = max(100, min(900, screen_y))
            
            return (screen_x, screen_y)
            
        except Exception as e:
            logger.error(f"Erro ao converter posição: {e}")
            return (960, 540)  # Centro da tela como fallback
    
    def _calculate_distance(self, pos1: Tuple[int, int], pos2: Tuple[int, int]) -> float:
        """Calcular distância entre duas posições"""
        return ((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)**0.5
    
    def _determine_lane(self, position: Tuple[int, int]) -> Lane:
        """Determinar lane baseado na posição no minimapa"""
        x, y = position
        
        # Divisão aproximada do minimapa em lanes
        if y < 100:  # Parte superior
            return Lane.TOP
        elif y > 200:  # Parte inferior
            return Lane.BOT
        elif 80 < x < 220:  # Centro
            return Lane.MID
        else:
            return Lane.JUNGLE
    
    def set_current_lane(self, lane: Lane):
        """Definir lane atual"""
        self.current_lane = lane
        logger.info(f"🎯 Lane atual definida: {lane.value}")
    
    def get_current_status(self) -> dict:
        """Obter status atual do sistema"""
        return {
            'current_lane': self.current_lane.value,
            'detected_enemies': len(self.detected_enemies),
            'last_detection': self.last_detection_time,
            'player_position': self.player_position
        }


# Instância global
minimap_system = MinimapSystem()
