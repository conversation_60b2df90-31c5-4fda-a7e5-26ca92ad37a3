"""
Utilitários para captura e análise de tela
"""
import cv2
import numpy as np
import pyautogui
from PIL import Image
from typing import Tuple, Optional, List
import time
import random

from .logger import logger

class ScreenCapture:
    """Classe para captura e análise de tela"""
    
    def __init__(self):
        # Desabilitar fail-safe do pyautogui
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.1
        
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Capturar tela ou região específica
        
        Args:
            region: (x, y, width, height) para capturar região específica
            
        Returns:
            Imagem capturada como array numpy
        """
        try:
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # Converter para formato OpenCV
            img_array = np.array(screenshot)
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
            
            return img_bgr
            
        except Exception as e:
            logger.error(f"Erro ao capturar tela: {e}")
            return None
    
    def find_template(self, template_path: str, threshold: float = 0.8, 
                     region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """
        Encontrar template na tela
        
        Args:
            template_path: Caminho para imagem template
            threshold: Limiar de confiança (0-1)
            region: Região para buscar
            
        Returns:
            Coordenadas (x, y) do centro do template encontrado
        """
        try:
            # Capturar tela
            screen = self.capture_screen(region)
            if screen is None:
                return None
            
            # Carregar template
            template = cv2.imread(template_path)
            if template is None:
                logger.error(f"Não foi possível carregar template: {template_path}")
                return None
            
            # Realizar matching
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # Calcular centro do template
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                # Ajustar coordenadas se foi usada região
                if region:
                    center_x += region[0]
                    center_y += region[1]
                
                logger.debug(f"Template encontrado: {template_path} em ({center_x}, {center_y}) com confiança {max_val:.2f}")
                return (center_x, center_y)
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao buscar template {template_path}: {e}")
            return None
    
    def wait_for_template(self, template_path: str, timeout: int = 30, 
                         threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Aguardar até que um template apareça na tela
        
        Args:
            template_path: Caminho para imagem template
            timeout: Tempo limite em segundos
            threshold: Limiar de confiança
            
        Returns:
            Coordenadas do template ou None se timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            coords = self.find_template(template_path, threshold)
            if coords:
                return coords
            
            time.sleep(0.5)
        
        logger.warning(f"Timeout aguardando template: {template_path}")
        return None
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Obter cor do pixel em coordenadas específicas
        
        Args:
            x, y: Coordenadas do pixel
            
        Returns:
            Cor RGB do pixel
        """
        try:
            screenshot = pyautogui.screenshot()
            pixel_color = screenshot.getpixel((x, y))
            return pixel_color
        except Exception as e:
            logger.error(f"Erro ao obter cor do pixel ({x}, {y}): {e}")
            return (0, 0, 0)

# Instância global
screen_capture = ScreenCapture()
